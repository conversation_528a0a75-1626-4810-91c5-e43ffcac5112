{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "JustCooked", "version": "0.1.0", "identifier": "com.justcooked.app", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"withGlobalTauri": false, "windows": [{"label": "main", "title": "JustCooked", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null, "capabilities": ["default"]}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["resources/*"], "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}}}